<template>
  <nav class="navbar" :class="{ 'scrolled': isScrolled }">
    <div class="container">
      <div class="nav-content">
        <!-- Logo -->
        <RouterLink to="/" class="logo">
          <div class="logo-icon">🍎</div>
          <span class="logo-text">Mu<PERSON><PERSON></span>
        </RouterLink>

        <!-- Desktop Navigation -->
        <div class="nav-links desktop-nav">
          <RouterLink to="/" class="nav-link">Ana Sayfa</RouterLink>
          <RouterLink to="/products" class="nav-link">Ürünler</RouterLink>
          <RouterLink to="/about" class="nav-link">Hakkımızda</RouterLink>
          <RouterLink to="/contact" class="nav-link">İletişim</RouterLink>
        </div>

        <!-- Cart and Mobile Menu -->
        <div class="nav-actions">
          <button class="cart-btn" @click="toggleCart">
            <span class="cart-icon">🛒</span>
            <span class="cart-count" v-if="cartItemCount > 0">{{ cartItemCount }}</span>
          </button>
          
          <button class="mobile-menu-btn" @click="toggleMobileMenu">
            <span class="hamburger" :class="{ 'active': isMobileMenuOpen }"></span>
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <Transition name="mobile-menu">
        <div v-if="isMobileMenuOpen" class="mobile-nav">
          <RouterLink to="/" class="mobile-nav-link" @click="closeMobileMenu">Ana Sayfa</RouterLink>
          <RouterLink to="/products" class="mobile-nav-link" @click="closeMobileMenu">Ürünler</RouterLink>
          <RouterLink to="/about" class="mobile-nav-link" @click="closeMobileMenu">Hakkımızda</RouterLink>
          <RouterLink to="/contact" class="mobile-nav-link" @click="closeMobileMenu">İletişim</RouterLink>
        </div>
      </Transition>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useCartStore } from '@/stores/cart'
import { storeToRefs } from 'pinia'

const cartStore = useCartStore()
const { itemCount: cartItemCount } = storeToRefs(cartStore)

const isScrolled = ref(false)
const isMobileMenuOpen = ref(false)

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

const toggleCart = () => {
  // This will be implemented when we create the cart component
  console.log('Toggle cart')
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: var(--transition-medium);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px var(--shadow-light);
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-dark);
  transition: var(--transition-fast);
}

.logo:hover {
  transform: scale(1.05);
}

.logo-icon {
  font-size: 2rem;
  margin-right: 0.5rem;
  animation: float 3s ease-in-out infinite;
}

.logo-text {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  position: relative;
  color: var(--text-dark);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  transition: var(--transition-fast);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  transition: var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.router-link-active::after {
  width: 100%;
}

.nav-link:hover {
  color: var(--primary-color);
}

/* Navigation Actions */
.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cart-btn {
  position: relative;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: var(--transition-fast);
}

.cart-btn:hover {
  background: var(--shadow-light);
  transform: scale(1.1);
}

.cart-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--primary-color);
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.2rem 0.4rem;
  border-radius: 50%;
  min-width: 1.2rem;
  text-align: center;
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.hamburger {
  display: block;
  width: 25px;
  height: 3px;
  background: var(--text-dark);
  position: relative;
  transition: var(--transition-fast);
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 25px;
  height: 3px;
  background: var(--text-dark);
  transition: var(--transition-fast);
}

.hamburger::before {
  top: -8px;
}

.hamburger::after {
  bottom: -8px;
}

.hamburger.active {
  background: transparent;
}

.hamburger.active::before {
  transform: rotate(45deg);
  top: 0;
}

.hamburger.active::after {
  transform: rotate(-45deg);
  bottom: 0;
}

/* Mobile Navigation */
.mobile-nav {
  display: flex;
  flex-direction: column;
  padding: 1rem 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.mobile-nav-link {
  color: var(--text-dark);
  text-decoration: none;
  padding: 1rem 0;
  font-weight: 500;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: var(--transition-fast);
}

.mobile-nav-link:hover {
  color: var(--primary-color);
  padding-left: 1rem;
}

/* Mobile Menu Transition */
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: all 0.3s ease;
}

.mobile-menu-enter-from,
.mobile-menu-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .logo-text {
    font-size: 1.2rem;
  }
  
  .nav-content {
    padding: 0.8rem 0;
  }
}

@media (max-width: 480px) {
  .logo-text {
    display: none;
  }
  
  .logo-icon {
    margin-right: 0;
  }
}
</style>
