<template>
  <div class="shopping-cart" :class="{ 'open': cartStore.isOpen }">
    <!-- Cart Overlay -->
    <Transition name="cart-overlay">
      <div v-if="cartStore.isOpen" class="cart-overlay" @click="cartStore.closeCart"></div>
    </Transition>

    <!-- Cart Sidebar -->
    <Transition name="cart-sidebar">
      <div v-if="cartStore.isOpen" class="cart-sidebar">
        <!-- Cart Header -->
        <div class="cart-header">
          <h3>Sepetim</h3>
          <button @click="cartStore.closeCart" class="close-btn">
            <span>&times;</span>
          </button>
        </div>

        <!-- Cart Content -->
        <div class="cart-content">
          <!-- Empty Cart -->
          <div v-if="cartStore.isEmpty" class="empty-cart">
            <div class="empty-icon">🛒</div>
            <h4>Sepetiniz Boş</h4>
            <p>Hen<PERSON>z sepetinize ürün eklemediniz.</p>
            <RouterLink to="/products" @click="cartStore.closeCart" class="btn btn-primary">
              Alışverişe Başla
            </RouterLink>
          </div>

          <!-- Cart Items -->
          <div v-else class="cart-items">
            <TransitionGroup name="cart-item" tag="div">
              <div 
                v-for="item in cartStore.items" 
                :key="item.product.id"
                class="cart-item"
              >
                <div class="item-image">
                  <img :src="item.product.image" :alt="item.product.name" />
                </div>
                
                <div class="item-details">
                  <h4 class="item-name">{{ item.product.name }}</h4>
                  <p class="item-price">{{ formatPrice(item.product.price) }} / kg</p>
                  
                  <div class="item-controls">
                    <div class="quantity-controls">
                      <button 
                        @click="cartStore.updateQuantity(item.product.id, item.quantity - 1)"
                        :disabled="item.quantity <= 1"
                        class="quantity-btn"
                      >
                        -
                      </button>
                      <span class="quantity">{{ item.quantity }} kg</span>
                      <button 
                        @click="cartStore.updateQuantity(item.product.id, item.quantity + 1)"
                        class="quantity-btn"
                      >
                        +
                      </button>
                    </div>
                    
                    <button 
                      @click="removeItem(item.product.id)"
                      class="remove-btn"
                      title="Sepetten Çıkar"
                    >
                      🗑️
                    </button>
                  </div>
                  
                  <div class="item-total">
                    Toplam: {{ formatPrice(item.product.price * item.quantity) }}
                  </div>
                </div>
              </div>
            </TransitionGroup>
          </div>
        </div>

        <!-- Cart Footer -->
        <div v-if="!cartStore.isEmpty" class="cart-footer">
          <div class="cart-summary">
            <div class="summary-row">
              <span>Ürün Sayısı:</span>
              <span>{{ cartStore.itemCount }} adet</span>
            </div>
            <div class="summary-row">
              <span>Kargo:</span>
              <span>{{ cartStore.totalPrice >= 100 ? 'Ücretsiz' : formatPrice(15) }}</span>
            </div>
            <div class="summary-row total-row">
              <span>Toplam:</span>
              <span>{{ formatPrice(cartStore.totalPrice + (cartStore.totalPrice >= 100 ? 0 : 15)) }}</span>
            </div>
          </div>
          
          <div class="cart-actions">
            <RouterLink 
              to="/cart" 
              @click="cartStore.closeCart"
              class="btn btn-secondary cart-btn"
            >
              Sepeti Görüntüle
            </RouterLink>
            <button 
              @click="proceedToCheckout"
              class="btn btn-primary cart-btn"
            >
              Siparişi Tamamla
            </button>
          </div>
          
          <button 
            @click="clearCart"
            class="clear-cart-btn"
          >
            Sepeti Temizle
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useCartStore } from '@/stores/cart'
import { gsap } from 'gsap'

const router = useRouter()
const cartStore = useCartStore()

const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY'
  }).format(price)
}

const removeItem = (productId: number) => {
  // Add remove animation
  const itemElement = document.querySelector(`[data-product-id="${productId}"]`)
  if (itemElement) {
    gsap.to(itemElement, {
      duration: 0.3,
      x: 100,
      opacity: 0,
      onComplete: () => {
        cartStore.removeFromCart(productId)
      }
    })
  } else {
    cartStore.removeFromCart(productId)
  }
}

const clearCart = () => {
  if (confirm('Sepetinizdeki tüm ürünleri silmek istediğinizden emin misiniz?')) {
    cartStore.clearCart()
  }
}

const proceedToCheckout = () => {
  cartStore.closeCart()
  router.push('/checkout')
}
</script>

<style scoped>
.shopping-cart {
  position: relative;
  z-index: 1000;
}

/* Cart Overlay */
.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.cart-overlay-enter-active,
.cart-overlay-leave-active {
  transition: opacity 0.3s ease;
}

.cart-overlay-enter-from,
.cart-overlay-leave-to {
  opacity: 0;
}

/* Cart Sidebar */
.cart-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.cart-sidebar-enter-active,
.cart-sidebar-leave-active {
  transition: transform 0.3s ease;
}

.cart-sidebar-enter-from,
.cart-sidebar-leave-to {
  transform: translateX(100%);
}

/* Cart Header */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.cart-header h3 {
  margin: 0;
  color: var(--text-dark);
  font-size: 1.3rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: var(--text-dark);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Cart Content */
.cart-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

/* Empty Cart */
.empty-cart {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-dark);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-cart h4 {
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.empty-cart p {
  margin-bottom: 2rem;
  opacity: 0.7;
}

/* Cart Items */
.cart-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.cart-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: var(--border-radius);
  transition: var(--transition-medium);
}

.cart-item:hover {
  background: #e9ecef;
}

.item-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.item-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-dark);
}

.item-price {
  margin: 0;
  font-size: 0.9rem;
  color: var(--primary-color);
  font-weight: 500;
}

.item-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border-radius: var(--border-radius);
  padding: 0.2rem;
}

.quantity-btn {
  width: 25px;
  height: 25px;
  border: none;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: var(--transition-fast);
}

.quantity-btn:hover:not(:disabled) {
  background: var(--primary-dark);
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity {
  font-size: 0.9rem;
  font-weight: 600;
  min-width: 40px;
  text-align: center;
}

.remove-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.3rem;
  border-radius: 50%;
  transition: var(--transition-fast);
}

.remove-btn:hover {
  background: rgba(231, 76, 60, 0.1);
}

.item-total {
  font-weight: 600;
  color: var(--text-dark);
  font-size: 0.9rem;
}

/* Cart Item Transitions */
.cart-item-enter-active,
.cart-item-leave-active {
  transition: all 0.3s ease;
}

.cart-item-enter-from,
.cart-item-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.cart-item-move {
  transition: transform 0.3s ease;
}

/* Cart Footer */
.cart-footer {
  border-top: 1px solid #eee;
  padding: 1.5rem;
  background: #f8f9fa;
}

.cart-summary {
  margin-bottom: 1.5rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.total-row {
  font-weight: 700;
  font-size: 1.1rem;
  color: var(--text-dark);
  border-top: 1px solid #ddd;
  padding-top: 0.5rem;
  margin-top: 1rem;
}

.cart-actions {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.cart-btn {
  width: 100%;
  padding: 0.8rem;
  text-align: center;
  text-decoration: none;
  font-weight: 600;
}

.clear-cart-btn {
  background: none;
  border: 1px solid #ddd;
  color: var(--text-dark);
  padding: 0.5rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.9rem;
  transition: var(--transition-fast);
}

.clear-cart-btn:hover {
  background: #f8f9fa;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 480px) {
  .cart-sidebar {
    width: 100vw;
  }
  
  .cart-item {
    flex-direction: column;
    text-align: center;
  }
  
  .item-image {
    align-self: center;
  }
  
  .item-controls {
    justify-content: center;
    gap: 1rem;
  }
}
</style>
